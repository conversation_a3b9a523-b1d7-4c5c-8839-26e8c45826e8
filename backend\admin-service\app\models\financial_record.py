"""
Financial record model for tracking income and expenses
"""

from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING

from sqlalchemy import Column, DateTime, Integer, String, Text, ForeignKey, Numeric, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User  # noqa: F401
    from .payment import Payment  # noqa: F401


class FinancialRecord(Base):
    __tablename__ = "financial_records"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Record identification
    record_number = Column(String(50), unique=True, index=True, nullable=False)
    
    # Financial details
    amount = Column(Numeric(precision=12, scale=2), nullable=False)
    currency = Column(String(3), nullable=False, default="USD")
    
    # Record type and category
    record_type = Column(String(20), nullable=False, index=True)  # INCOME, EXPENSE
    category = Column(String(100), nullable=False, index=True)  # Course fees, salaries, utilities, etc.
    subcategory = Column(String(100), nullable=True)
    
    # Description
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Related entities
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Related user (student, staff)
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # Admin who created record
    payment_id = Column(Integer, ForeignKey("payments.id"), nullable=True)  # Related payment
    
    # Status and approval
    status = Column(String(20), nullable=False, default="PENDING", index=True)
    # PENDING, APPROVED, REJECTED, CANCELLED
    
    is_recurring = Column(Boolean, default=False, nullable=False)
    recurring_frequency = Column(String(20), nullable=True)  # monthly, quarterly, yearly
    
    # Dates
    transaction_date = Column(DateTime, nullable=False, index=True)
    due_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    approved_at = Column(DateTime, nullable=True)
    
    # Tax and accounting
    is_taxable = Column(Boolean, default=True, nullable=False)
    tax_rate = Column(Numeric(precision=5, scale=2), nullable=True)  # Tax percentage
    tax_amount = Column(Numeric(precision=10, scale=2), nullable=True)
    
    # Reference information
    reference_number = Column(String(100), nullable=True)  # Invoice number, receipt number, etc.
    external_reference = Column(String(100), nullable=True)  # Bank reference, etc.
    
    # Attachments and notes
    attachment_urls = Column(Text, nullable=True)  # JSON array of file URLs
    notes = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="financial_records")
    created_by = relationship("User", foreign_keys=[created_by_id], backref="created_financial_records")
    payment = relationship("Payment", backref="financial_records")
    
    @property
    def net_amount(self) -> Decimal:
        """Get net amount after tax"""
        if self.tax_amount:
            return self.amount - self.tax_amount
        return self.amount
    
    @property
    def is_income(self) -> bool:
        """Check if record is income"""
        return self.record_type == FinancialRecordType.INCOME
    
    @property
    def is_expense(self) -> bool:
        """Check if record is expense"""
        return self.record_type == FinancialRecordType.EXPENSE
    
    def __repr__(self) -> str:
        return (
            f"<FinancialRecord(id={self.id}, record_number='{self.record_number}', "
            f"type='{self.record_type}', amount={self.amount})>"
        )


class FinancialRecordType:
    """Financial record type constants"""
    INCOME = "INCOME"
    EXPENSE = "EXPENSE"


class FinancialRecordStatus:
    """Financial record status constants"""
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"


class IncomeCategory:
    """Income category constants"""
    COURSE_FEES = "course_fees"
    REGISTRATION_FEES = "registration_fees"
    CONSULTATION_FEES = "consultation_fees"
    WORKSHOP_FEES = "workshop_fees"
    CERTIFICATION_FEES = "certification_fees"
    OTHER_INCOME = "other_income"


class ExpenseCategory:
    """Expense category constants"""
    SALARIES = "salaries"
    RENT = "rent"
    UTILITIES = "utilities"
    EQUIPMENT = "equipment"
    SUPPLIES = "supplies"
    MARKETING = "marketing"
    INSURANCE = "insurance"
    MAINTENANCE = "maintenance"
    TRAVEL = "travel"
    TRAINING = "training"
    SOFTWARE_LICENSES = "software_licenses"
    OTHER_EXPENSES = "other_expenses"


class RecurringFrequency:
    """Recurring frequency constants"""
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
