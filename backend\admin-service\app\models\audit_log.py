"""
Audit log model for tracking user actions
"""

from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import Column, DateTime, Integer, String, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User  # noqa: F401


class AuditLog(Base):
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # User who performed the action
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Action details
    action = Column(String(100), nullable=False, index=True)  # e.g., "CREATE", "UPDATE", "DELETE"
    resource_type = Column(String(100), nullable=False, index=True)  # e.g., "User", "Payment"
    resource_id = Column(String(100), nullable=True, index=True)  # ID of the affected resource
    
    # Request details
    endpoint = Column(String(500), nullable=True)
    method = Column(String(10), nullable=True)  # HTTP method
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(Text, nullable=True)
    
    # Change details
    old_values = Column(JSON, nullable=True)  # Previous values (for updates)
    new_values = Column(JSON, nullable=True)  # New values
    
    # Additional context
    description = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)  # Additional metadata
    
    # Status
    status = Column(String(20), nullable=False, default="SUCCESS")  # SUCCESS, FAILED, ERROR
    error_message = Column(Text, nullable=True)
    
    # Timestamp
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        return (
            f"<AuditLog(id={self.id}, action='{self.action}', "
            f"resource='{self.resource_type}:{self.resource_id}', "
            f"user_id={self.user_id})>"
        )


class AuditActions:
    """Standard audit action types"""
    CREATE = "CREATE"
    READ = "READ"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    LOGIN_FAILED = "LOGIN_FAILED"
    PASSWORD_CHANGE = "PASSWORD_CHANGE"
    PERMISSION_GRANT = "PERMISSION_GRANT"
    PERMISSION_REVOKE = "PERMISSION_REVOKE"
    EXPORT = "EXPORT"
    IMPORT = "IMPORT"
    PAYMENT_PROCESS = "PAYMENT_PROCESS"
    PAYMENT_REFUND = "PAYMENT_REFUND"
    CONFIG_CHANGE = "CONFIG_CHANGE"


class AuditResourceTypes:
    """Standard resource types for audit logging"""
    USER = "User"
    ROLE = "Role"
    PAYMENT = "Payment"
    FINANCIAL_RECORD = "FinancialRecord"
    SYSTEM_CONFIG = "SystemConfig"
    AUTH = "Authentication"
