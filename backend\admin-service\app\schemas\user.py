"""
User schemas for API serialization
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, validator


# Shared properties
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False
    is_verified: Optional[bool] = False
    role_id: Optional[int] = None
    notes: Optional[str] = None


# Properties to receive via API on creation
class UserCreate(UserBase):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    
    @validator("password")
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v


# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None
    
    @validator("password")
    def validate_password(cls, v):
        if v is not None and len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v


class UserInDBBase(UserBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True


# Additional properties to return via API
class UserResponse(UserInDBBase):
    full_name: Optional[str] = None
    
    @validator("full_name", pre=True, always=True)
    def set_full_name(cls, v, values):
        if "first_name" in values and "last_name" in values:
            return f"{values['first_name']} {values['last_name']}"
        return v


# Additional properties stored in DB
class UserInDB(UserInDBBase):
    hashed_password: str
