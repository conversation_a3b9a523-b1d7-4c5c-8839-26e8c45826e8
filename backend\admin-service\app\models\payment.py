"""
Payment model for financial transactions
"""

from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING

from sqlalchemy import Column, DateTime, Integer, String, Text, ForeignKey, Numeric, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User  # noqa: F401


class Payment(Base):
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Payment identification
    payment_id = Column(String(100), unique=True, index=True, nullable=False)  # External payment ID
    transaction_id = Column(String(100), unique=True, index=True, nullable=True)  # Bank/gateway transaction ID
    
    # Amount and currency
    amount = Column(Numeric(precision=10, scale=2), nullable=False)
    currency = Column(String(3), nullable=False, default="USD")
    
    # Payment details
    payment_method = Column(String(50), nullable=False)  # card, bank_transfer, cash, etc.
    payment_gateway = Column(String(50), nullable=True)  # stripe, paypal, etc.
    
    # Status
    status = Column(String(20), nullable=False, default="PENDING", index=True)
    # PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED, REFUNDED, PARTIALLY_REFUNDED
    
    # Related entities
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Payer
    processed_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Admin who processed
    
    # Payment purpose
    purpose = Column(String(200), nullable=False)  # Course fee, registration, etc.
    description = Column(Text, nullable=True)
    
    # Gateway response data
    gateway_response = Column(JSON, nullable=True)
    
    # Refund information
    refunded_amount = Column(Numeric(precision=10, scale=2), nullable=True, default=0)
    refund_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    processed_at = Column(DateTime, nullable=True)
    
    # Due date for pending payments
    due_date = Column(DateTime, nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="payments")
    processed_by = relationship("User", foreign_keys=[processed_by_id], backref="processed_payments")
    
    @property
    def is_completed(self) -> bool:
        """Check if payment is completed"""
        return self.status == PaymentStatus.COMPLETED
    
    @property
    def is_refundable(self) -> bool:
        """Check if payment can be refunded"""
        return (
            self.status == PaymentStatus.COMPLETED and
            (self.refunded_amount or 0) < self.amount
        )
    
    @property
    def remaining_amount(self) -> Decimal:
        """Get remaining amount after refunds"""
        return self.amount - (self.refunded_amount or 0)
    
    def __repr__(self) -> str:
        return (
            f"<Payment(id={self.id}, payment_id='{self.payment_id}', "
            f"amount={self.amount}, status='{self.status}')>"
        )


class PaymentStatus:
    """Payment status constants"""
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    REFUNDED = "REFUNDED"
    PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED"


class PaymentMethod:
    """Payment method constants"""
    CARD = "card"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"
    DIGITAL_WALLET = "digital_wallet"
    CRYPTOCURRENCY = "cryptocurrency"
    CHECK = "check"


class PaymentGateway:
    """Payment gateway constants"""
    STRIPE = "stripe"
    PAYPAL = "paypal"
    SQUARE = "square"
    MANUAL = "manual"
