"""
<PERSON><PERSON>t to create initial database migration
"""

import subprocess
import sys
import os

def create_initial_migration():
    """Create the initial database migration"""
    try:
        print("Creating initial database migration...")
        
        # Change to the backend directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # Run alembic revision command
        result = subprocess.run([
            sys.executable, "-m", "alembic", "revision", "--autogenerate", 
            "-m", "Initial migration"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Initial migration created successfully!")
            print(result.stdout)
        else:
            print("❌ Failed to create migration:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error creating migration: {e}")


def upgrade_database():
    """Apply migrations to database"""
    try:
        print("Applying migrations to database...")
        
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database migrations applied successfully!")
            print(result.stdout)
        else:
            print("❌ Failed to apply migrations:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error applying migrations: {e}")


def initialize_database():
    """Initialize database with default data"""
    try:
        print("Initializing database with default data...")
        
        from app.db.session import SessionLocal
        from app.db.init_db import init_db
        
        db = SessionLocal()
        try:
            init_db(db)
            print("✅ Database initialized successfully!")
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error initializing database: {e}")


if __name__ == "__main__":
    print("🚀 Setting up database for Innovative Centre Admin Service\n")
    
    create_initial_migration()
    print()
    upgrade_database()
    print()
    initialize_database()
    
    print("\n🎉 Database setup complete!")
    print("You can now start the server with: uvicorn main:app --reload")
