# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment and Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
aiofiles==23.2.1

# Validation and Serialization
email-validator==2.1.0
phonenumbers==8.13.26

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Logging and Monitoring
structlog==23.2.0

# CORS
python-cors==1.7.0

# Date and Time
python-dateutil==2.8.2

# File handling
pillow==10.1.0

# Background tasks
celery==5.3.4

# API Documentation
fastapi-users==12.1.2
