{"python.defaultInterpreterPath": "./backend/admin-service/venv/Scripts/python.exe", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "files.associations": {"*.env": "dotenv"}, "typescript.preferences.importModuleSpecifier": "relative", "eslint.workingDirectories": ["frontend/admin-portal", "frontend/staff-portal", "frontend/reception-portal"], "files.exclude": {"**/node_modules": true, "**/__pycache__": true, "**/.pytest_cache": true, "**/.mypy_cache": true, "**/venv": true, "**/.env": false}, "search.exclude": {"**/node_modules": true, "**/venv": true, "**/.git": true}}