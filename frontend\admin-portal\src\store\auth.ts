'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api, apiClient } from '@/lib/api'
import { User, LoginCredentials, LoginResponse, AuthState } from '@/types/auth'
import { toast } from 'react-hot-toast'

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>
  logout: () => void
  refreshUser: () => Promise<void>
  updateUser: (user: User) => void
  initialize: () => Promise<void>
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,

      // Actions
      login: async (credentials: LoginCredentials): Promise<boolean> => {
        try {
          set({ isLoading: true })
          
          const response = await api.post<LoginResponse>('/api/v1/auth/login/json', credentials)
          
          const { access_token, user } = response
          
          // Store token and user
          apiClient.setToken(access_token)
          
          set({
            user,
            token: access_token,
            isAuthenticated: true,
            isLoading: false,
          })
          
          toast.success(`Welcome back, ${user.first_name}!`)
          return true
        } catch (error) {
          set({ isLoading: false })
          console.error('Login error:', error)
          return false
        }
      },

      logout: () => {
        // Clear token and user data
        apiClient.removeToken()
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
        
        toast.success('Logged out successfully')
        
        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login'
        }
      },

      refreshUser: async () => {
        try {
          const user = await api.get<User>('/api/v1/users/me')
          set({ user })
        } catch (error) {
          console.error('Failed to refresh user:', error)
          // If refresh fails, logout user
          get().logout()
        }
      },

      updateUser: (user: User) => {
        set({ user })
      },

      initialize: async () => {
        try {
          set({ isLoading: true })
          
          // Check if token exists in localStorage
          const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
          
          if (!token) {
            set({ isLoading: false })
            return
          }
          
          // Verify token by fetching user data
          const user = await api.get<User>('/api/v1/users/me')
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error) {
          // Token is invalid, clear it
          apiClient.removeToken()
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Initialize auth state on app load
if (typeof window !== 'undefined') {
  useAuthStore.getState().initialize()
}
