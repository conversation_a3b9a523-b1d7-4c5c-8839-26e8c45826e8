{"version": "0.2.0", "configurations": [{"name": "FastAPI Admin Service", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/admin-service/venv/Scripts/uvicorn", "args": ["main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "cwd": "${workspaceFolder}/backend/admin-service", "env": {"PYTHONPATH": "${workspaceFolder}/backend/admin-service"}, "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env"}, {"name": "Next.js Admin Portal", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/admin-portal/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/frontend/admin-portal", "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env"}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env"}], "compounds": [{"name": "Launch Full Stack", "configurations": ["FastAPI Admin Service", "Next.js Admin Portal"]}]}