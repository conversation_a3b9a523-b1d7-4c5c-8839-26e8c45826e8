export interface User {
  id: number
  email: string
  first_name: string
  last_name: string
  full_name: string
  phone?: string
  avatar_url?: string
  is_active: boolean
  is_superuser: boolean
  is_verified: boolean
  role_id?: number
  created_at: string
  updated_at: string
  last_login?: string
  notes?: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface PasswordChangeData {
  current_password: string
  new_password: string
}

export interface UserCreateData {
  email: string
  password: string
  first_name: string
  last_name: string
  phone?: string
  is_active?: boolean
  is_superuser?: boolean
  is_verified?: boolean
  role_id?: number
  notes?: string
}

export interface UserUpdateData {
  email?: string
  first_name?: string
  last_name?: string
  phone?: string
  is_active?: boolean
  is_superuser?: boolean
  is_verified?: boolean
  role_id?: number
  notes?: string
}
