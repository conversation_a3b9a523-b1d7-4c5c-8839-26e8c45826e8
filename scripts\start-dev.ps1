# Development Server Start Script for Windows

Write-Host "Starting Innovative Centre Platform Development Servers..." -ForegroundColor Green

# Function to start a process in a new window
function Start-DevServer($title, $command, $workingDir) {
    Write-Host "Starting $title..." -ForegroundColor Blue
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$workingDir'; $command" -WindowStyle Normal
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "Error: .env file not found. Please run setup-dev.ps1 first." -ForegroundColor Red
    exit 1
}

# Start database services with Docker Compose
Write-Host "Starting database services..." -ForegroundColor Blue
docker-compose up -d postgres redis

# Wait a moment for databases to start
Start-Sleep -Seconds 5

# Start backend server
$backendPath = Resolve-Path "backend/admin-service"
Start-DevServer "Admin Service (FastAPI)" "& venv\Scripts\Activate.ps1; uvicorn main:app --reload --host 0.0.0.0 --port 8000" $backendPath

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend server
$frontendPath = Resolve-Path "frontend/admin-portal"
Start-DevServer "Admin Portal (Next.js)" "npm run dev" $frontendPath

Write-Host "Development servers are starting..." -ForegroundColor Green
Write-Host "Services will be available at:" -ForegroundColor Cyan
Write-Host "- Admin Portal: http://localhost:3000" -ForegroundColor White
Write-Host "- Admin API: http://localhost:8000" -ForegroundColor White
Write-Host "- API Documentation: http://localhost:8000/docs" -ForegroundColor White
Write-Host "- PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "- Redis: localhost:6379" -ForegroundColor White

Write-Host "Press any key to stop all services..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop all services
Write-Host "Stopping services..." -ForegroundColor Red
docker-compose down
Get-Process | Where-Object {$_.ProcessName -eq "node" -or $_.ProcessName -eq "python"} | Stop-Process -Force
