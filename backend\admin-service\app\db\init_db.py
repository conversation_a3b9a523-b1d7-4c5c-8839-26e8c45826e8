"""
Database initialization script
"""

from sqlalchemy.orm import Session

from app.core.config import settings
from app.db import base  # noqa: F401
from app.models.role import Role, Permissions
from app.models.user import User
from app.schemas.user import UserCreate
from app.services.user_service import user_service


def init_db(db: Session) -> None:
    """
    Initialize database with default data
    """
    # Create default roles
    create_default_roles(db)
    
    # Create superuser
    create_superuser(db)


def create_default_roles(db: Session) -> None:
    """Create default system roles"""
    
    # Super Admin Role
    super_admin_role = db.query(Role).filter(Role.name == "super_admin").first()
    if not super_admin_role:
        super_admin_role = Role(
            name="super_admin",
            display_name="Super Administrator",
            description="Full system access with all permissions",
            permissions=Permissions.get_all_permissions(),
            is_system=True
        )
        db.add(super_admin_role)
    
    # Admin Role
    admin_role = db.query(Role).filter(Role.name == "admin").first()
    if not admin_role:
        admin_role = Role(
            name="admin",
            display_name="Administrator",
            description="Administrative access with most permissions",
            permissions=[
                Permissions.USER_READ,
                Permissions.USER_CREATE,
                Permissions.USER_UPDATE,
                Permissions.ROLE_READ,
                Permissions.PAYMENT_READ,
                Permissions.PAYMENT_CREATE,
                Permissions.PAYMENT_UPDATE,
                Permissions.PAYMENT_PROCESS,
                Permissions.FINANCIAL_READ,
                Permissions.FINANCIAL_CREATE,
                Permissions.FINANCIAL_UPDATE,
                Permissions.FINANCIAL_REPORTS,
                Permissions.SYSTEM_CONFIG_READ,
                Permissions.AUDIT_READ,
            ],
            is_system=True
        )
        db.add(admin_role)
    
    # Financial Manager Role
    financial_role = db.query(Role).filter(Role.name == "financial_manager").first()
    if not financial_role:
        financial_role = Role(
            name="financial_manager",
            display_name="Financial Manager",
            description="Financial and payment management access",
            permissions=[
                Permissions.PAYMENT_READ,
                Permissions.PAYMENT_CREATE,
                Permissions.PAYMENT_UPDATE,
                Permissions.PAYMENT_PROCESS,
                Permissions.FINANCIAL_READ,
                Permissions.FINANCIAL_CREATE,
                Permissions.FINANCIAL_UPDATE,
                Permissions.FINANCIAL_REPORTS,
                Permissions.AUDIT_READ,
            ],
            is_system=True
        )
        db.add(financial_role)
    
    # Staff Role
    staff_role = db.query(Role).filter(Role.name == "staff").first()
    if not staff_role:
        staff_role = Role(
            name="staff",
            display_name="Staff Member",
            description="Basic staff access for daily operations",
            permissions=[
                Permissions.USER_READ,
                Permissions.PAYMENT_READ,
                Permissions.FINANCIAL_READ,
            ],
            is_system=True
        )
        db.add(staff_role)
    
    # Reception Role
    reception_role = db.query(Role).filter(Role.name == "reception").first()
    if not reception_role:
        reception_role = Role(
            name="reception",
            display_name="Reception Staff",
            description="Reception desk operations access",
            permissions=[
                Permissions.USER_READ,
                Permissions.PAYMENT_READ,
                Permissions.PAYMENT_CREATE,
            ],
            is_system=True
        )
        db.add(reception_role)
    
    db.commit()


def create_superuser(db: Session) -> None:
    """Create initial superuser"""
    user = user_service.get_by_email(db, email=settings.FIRST_SUPERUSER_EMAIL)
    if not user:
        # Get super admin role
        super_admin_role = db.query(Role).filter(Role.name == "super_admin").first()
        
        user_in = UserCreate(
            email=settings.FIRST_SUPERUSER_EMAIL,
            password=settings.FIRST_SUPERUSER_PASSWORD,
            first_name="Super",
            last_name="Admin",
            is_superuser=True,
            is_active=True,
            is_verified=True,
            role_id=super_admin_role.id if super_admin_role else None,
        )
        user = user_service.create(db, obj_in=user_in)
        print(f"Superuser created: {user.email}")
    else:
        print(f"Superuser already exists: {user.email}")
