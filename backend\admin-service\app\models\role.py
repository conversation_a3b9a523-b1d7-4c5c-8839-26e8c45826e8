"""
Role model for role-based access control
"""

from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, Text, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User  # noqa: F401


class Role(Base):
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Permissions stored as JSON
    permissions = Column(JSON, nullable=False, default=list)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)  # System roles cannot be deleted
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="role")
    
    def has_permission(self, permission: str) -> bool:
        """Check if role has a specific permission"""
        return permission in self.permissions
    
    def add_permission(self, permission: str) -> None:
        """Add a permission to the role"""
        if permission not in self.permissions:
            self.permissions.append(permission)
    
    def remove_permission(self, permission: str) -> None:
        """Remove a permission from the role"""
        if permission in self.permissions:
            self.permissions.remove(permission)
    
    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name='{self.name}', display_name='{self.display_name}')>"


# Default permissions for the system
class Permissions:
    # User Management
    USER_READ = "user:read"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    
    # Role Management
    ROLE_READ = "role:read"
    ROLE_CREATE = "role:create"
    ROLE_UPDATE = "role:update"
    ROLE_DELETE = "role:delete"
    
    # Payment Management
    PAYMENT_READ = "payment:read"
    PAYMENT_CREATE = "payment:create"
    PAYMENT_UPDATE = "payment:update"
    PAYMENT_DELETE = "payment:delete"
    PAYMENT_PROCESS = "payment:process"
    
    # Financial Management
    FINANCIAL_READ = "financial:read"
    FINANCIAL_CREATE = "financial:create"
    FINANCIAL_UPDATE = "financial:update"
    FINANCIAL_DELETE = "financial:delete"
    FINANCIAL_REPORTS = "financial:reports"
    
    # System Configuration
    SYSTEM_CONFIG_READ = "system:config:read"
    SYSTEM_CONFIG_UPDATE = "system:config:update"
    
    # Audit Logs
    AUDIT_READ = "audit:read"
    AUDIT_EXPORT = "audit:export"
    
    # Admin permissions
    ADMIN_ALL = "admin:all"
    
    @classmethod
    def get_all_permissions(cls) -> list:
        """Get all available permissions"""
        return [
            value for name, value in cls.__dict__.items()
            if not name.startswith('_') and isinstance(value, str) and ':' in value
        ]
