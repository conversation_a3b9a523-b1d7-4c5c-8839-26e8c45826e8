# Innovative Centre Platform

A comprehensive CRM platform for the Innovative Centre with separate portals for admin, staff, and reception management.

## Architecture Overview

The platform follows a microservices architecture with:
- **Frontend**: Next.js with TypeScript for all portals
- **Backend**: FastAPI with Python for all services
- **Database**: PostgreSQL for primary data, Redis for caching
- **Authentication**: JWT-based with role-based access control

## Project Structure

```
innovative-centre-platform/
├── frontend/
│   ├── admin-portal/          # Admin Portal (Next.js)
│   ├── staff-portal/          # Staff Portal (Future Phase)
│   └── reception-portal/      # Reception Portal (Future Phase)
├── backend/
│   ├── admin-service/         # Admin Service (FastAPI)
│   ├── staff-service/         # Staff Service (Future Phase)
│   ├── reception-service/     # Reception Service (Future Phase)
│   └── shared/               # Shared utilities and models
├── database/
│   ├── migrations/           # Database migrations
│   └── schemas/              # Database schemas
├── docs/                     # Documentation
├── scripts/                  # Development and deployment scripts
└── docker/                   # Docker configurations
```

## Development Phases

### Phase 1: Admin Portal (Current Focus)
- ✅ Project setup and architecture
- 🔄 Backend infrastructure (FastAPI)
- 🔄 Admin portal frontend (Next.js)
- 🔄 Admin services implementation
- 🔄 Integration and testing

### Phase 2: Staff Portal (Future)
- Staff service backend
- Staff portal frontend
- Staff-specific features

### Phase 3: Reception Portal (Future)
- Reception service backend
- Reception portal frontend
- Reception-specific features

## Admin Portal Features

### High Security Admin Services
- **Payments**: Payment processing, transaction history, financial reporting
- **Financial**: Revenue tracking, expense management, financial analytics
- **User Management**: User lifecycle, roles, permissions
- **System Configuration**: System settings, administrative controls
- **Audit Logs**: Activity logging, security monitoring

## Technology Stack

### Frontend
- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui or similar
- **State Management**: Zustand or React Context
- **HTTP Client**: Axios

### Backend
- **Framework**: FastAPI
- **Language**: Python 3.11+
- **Database ORM**: SQLAlchemy
- **Authentication**: JWT with python-jose
- **Validation**: Pydantic
- **Testing**: Pytest

### Database
- **Primary**: PostgreSQL
- **Cache**: Redis
- **Migrations**: Alembic

### DevOps
- **Containerization**: Docker
- **Process Management**: Docker Compose
- **Environment**: .env files
- **Version Control**: Git

## Getting Started

### Prerequisites
- Node.js 18+
- Python 3.11+
- PostgreSQL
- Redis
- Docker (optional)

### Development Setup
1. Clone the repository
2. Set up backend environment
3. Set up frontend environment
4. Configure database
5. Run development servers

## Security Considerations

- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization
- Audit logging for all admin actions
- Secure password policies
- HTTPS enforcement

## API Documentation

API documentation will be automatically generated using FastAPI's built-in Swagger UI and available at `/docs` endpoint.

## Contributing

Please read the contributing guidelines before submitting pull requests.

## License

[License information to be added]
