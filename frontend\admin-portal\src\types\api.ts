export interface ApiResponse<T = any> {
  data?: T
  message?: string
  error?: string
  status: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

export interface ApiError {
  message: string
  status: number
  details?: any
}

export interface HealthCheck {
  status: string
  service: string
  message: string
  database?: string
  checks?: Record<string, {
    status: string
    message: string
  }>
}
