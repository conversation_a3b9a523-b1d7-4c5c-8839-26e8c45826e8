# Innovative Centre Platform - Development Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.11+
- PostgreSQL (or Docker)
- Redis (or Docker)
- Git

### Automated Setup
Run the setup script to automatically configure the entire development environment:

```powershell
.\setup-project.ps1
```

### Manual Setup

1. **Clone and Setup Environment**
   ```bash
   git clone <repository-url>
   cd innovative-centre-platform
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Backend Setup**
   ```bash
   cd backend/admin-service
   python -m venv venv
   venv\Scripts\activate  # Windows
   # source venv/bin/activate  # Linux/Mac
   pip install -r requirements.txt
   ```

3. **Frontend Setup**
   ```bash
   cd frontend/admin-portal
   npm install
   ```

4. **Database Setup**
   ```bash
   # Start databases with Docker
   docker-compose up -d postgres redis
   
   # Or install PostgreSQL and Redis manually
   # PostgreSQL: localhost:5432, database: innovative_centre
   # Redis: localhost:6379
   
   # Run migrations
   cd backend/admin-service
   alembic upgrade head
   python create_migration.py
   ```

## 🏃‍♂️ Running the Application

### Development Servers

**Backend (FastAPI)**
```bash
cd backend/admin-service
venv\Scripts\activate
uvicorn main:app --reload
```
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs

**Frontend (Next.js)**
```bash
cd frontend/admin-portal
npm run dev
```
- Admin Portal: http://localhost:3000

### Using Docker Compose
```bash
docker-compose up -d
```

## 🔐 Default Credentials

**Super Admin Account**
- Email: `<EMAIL>`
- Password: `changeme`

⚠️ **Important**: Change the default password immediately in production!

## 📁 Project Structure

```
innovative-centre-platform/
├── backend/
│   ├── admin-service/          # FastAPI admin service
│   │   ├── app/
│   │   │   ├── api/           # API endpoints
│   │   │   ├── core/          # Core configuration
│   │   │   ├── db/            # Database configuration
│   │   │   ├── models/        # SQLAlchemy models
│   │   │   ├── schemas/       # Pydantic schemas
│   │   │   ├── services/      # Business logic
│   │   │   └── utils/         # Utilities
│   │   ├── alembic/           # Database migrations
│   │   └── tests/             # Backend tests
│   ├── staff-service/         # Future: Staff service
│   ├── reception-service/     # Future: Reception service
│   └── shared/                # Shared utilities
├── frontend/
│   ├── admin-portal/          # Next.js admin portal
│   │   ├── src/
│   │   │   ├── app/           # Next.js app router
│   │   │   ├── components/    # React components
│   │   │   ├── lib/           # Libraries and utilities
│   │   │   ├── hooks/         # Custom React hooks
│   │   │   ├── store/         # State management
│   │   │   └── types/         # TypeScript types
│   │   └── public/            # Static assets
│   ├── staff-portal/          # Future: Staff portal
│   └── reception-portal/      # Future: Reception portal
├── database/                  # Database schemas and migrations
├── docs/                      # Documentation
├── scripts/                   # Development scripts
└── docker/                    # Docker configurations
```

## 🛠️ Development Workflow

### Backend Development

1. **Adding New Models**
   - Create model in `backend/admin-service/app/models/`
   - Add to `app/db/base.py` for Alembic detection
   - Create migration: `alembic revision --autogenerate -m "Description"`
   - Apply migration: `alembic upgrade head`

2. **Adding New API Endpoints**
   - Create endpoint in `app/api/v1/endpoints/`
   - Add to router in `app/api/v1/api.py`
   - Create corresponding schemas in `app/schemas/`

3. **Adding Business Logic**
   - Create service in `app/services/`
   - Extend `BaseService` for CRUD operations
   - Add custom business methods

### Frontend Development

1. **Adding New Pages**
   - Create page in `src/app/` following Next.js app router structure
   - Use `ProtectedRoute` component for authenticated pages

2. **Adding Components**
   - Create reusable components in `src/components/`
   - Follow the established component structure

3. **State Management**
   - Use Zustand stores in `src/store/`
   - Follow the pattern established in `auth.ts`

## 🧪 Testing

### Backend Tests
```bash
cd backend/admin-service
pytest
```

### Frontend Tests
```bash
cd frontend/admin-portal
npm test
```

## 📦 Building for Production

### Backend
```bash
cd backend/admin-service
pip install -r requirements.txt
# Configure production environment variables
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Frontend
```bash
cd frontend/admin-portal
npm run build
npm start
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/innovative_centre

# JWT Security
JWT_SECRET_KEY=your-secret-key-change-in-production

# API Configuration
API_V1_STR=/api/v1
BACKEND_CORS_ORIGINS=["http://localhost:3000"]

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 🚦 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh token

### Users
- `GET /api/v1/users/me` - Get current user
- `GET /api/v1/users/` - List users (admin)
- `POST /api/v1/users/` - Create user (admin)
- `PUT /api/v1/users/{id}` - Update user (admin)

### Health
- `GET /health` - Basic health check
- `GET /api/v1/health/` - Detailed health check

## 🎯 Next Development Phases

### Phase 2: Staff Portal
- Staff service backend
- Staff portal frontend
- Staff-specific features (leads, courses, groups, teachers, cabinets)

### Phase 3: Reception Portal
- Reception service backend
- Reception portal frontend
- Reception-specific features

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Add tests
4. Update documentation
5. Submit a pull request

## 📞 Support

For questions or issues, please refer to the project documentation or contact the development team.
