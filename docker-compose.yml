version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: innovative_postgres
    environment:
      POSTGRES_DB: innovative_centre
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - innovative_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: innovative_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - innovative_network

  # Admin Service (FastAPI)
  admin-service:
    build:
      context: ./backend/admin-service
      dockerfile: Dockerfile
    container_name: innovative_admin_service
    environment:
      - DATABASE_URL=********************************************/innovative_centre
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend/admin-service:/app
    networks:
      - innovative_network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Admin Portal (Next.js)
  admin-portal:
    build:
      context: ./frontend/admin-portal
      dockerfile: Dockerfile
    container_name: innovative_admin_portal
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - admin-service
    volumes:
      - ./frontend/admin-portal:/app
      - /app/node_modules
    networks:
      - innovative_network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  innovative_network:
    driver: bridge
