# Innovative Centre Platform Setup Script

Write-Host "🚀 Setting up Innovative Centre Platform..." -ForegroundColor Green
Write-Host "This script will set up the complete development environment." -ForegroundColor Cyan

# Function to check if a command exists
function Test-Command($command) {
    try {
        if (Get-Command $command -ErrorAction Stop) {
            return $true
        }
    }
    catch {
        return $false
    }
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "`n📋 Checking prerequisites..." -ForegroundColor Blue

    $allGood = $true

    # Check Node.js
    if (Test-Command "node") {
        $nodeVersion = node --version
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
        $allGood = $false
    }

    # Check Python
    if (Test-Command "python") {
        $pythonVersion = python --version
        Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Python not found. Please install Python 3.11+ from https://python.org/" -ForegroundColor Red
        $allGood = $false
    }

    # Check Docker (optional)
    if (Test-Command "docker") {
        Write-Host "✅ Docker: Available" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  Docker not found. You can install it later for containerized development." -ForegroundColor Yellow
    }

    # Check Git
    if (Test-Command "git") {
        Write-Host "✅ Git: Available" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Git not found. Please install Git from https://git-scm.com/" -ForegroundColor Red
        $allGood = $false
    }

    return $allGood
}

# Function to setup environment file
function Setup-Environment {
    Write-Host "`n🔧 Setting up environment configuration..." -ForegroundColor Blue

    if (-not (Test-Path ".env")) {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Created .env file from .env.example" -ForegroundColor Green
        Write-Host "📝 Please review and update the .env file with your specific configuration" -ForegroundColor Yellow
    }
    else {
        Write-Host "✅ .env file already exists" -ForegroundColor Green
    }
}

# Function to setup backend
function Setup-Backend {
    Write-Host "`n🐍 Setting up backend (FastAPI)..." -ForegroundColor Blue

    Set-Location "backend/admin-service"

    # Create virtual environment
    if (-not (Test-Path "venv")) {
        Write-Host "Creating Python virtual environment..." -ForegroundColor Cyan
        python -m venv venv
        Write-Host "✅ Virtual environment created" -ForegroundColor Green
    }

    # Activate virtual environment and install dependencies
    Write-Host "Installing Python dependencies..." -ForegroundColor Cyan
    & "venv\Scripts\Activate.ps1"
    pip install --upgrade pip
    pip install -r requirements.txt
    Write-Host "✅ Backend dependencies installed" -ForegroundColor Green

    Set-Location "../.."
}

# Function to setup frontend
function Setup-Frontend {
    Write-Host "`n⚛️  Setting up frontend (Next.js)..." -ForegroundColor Blue

    Set-Location "frontend/admin-portal"

    # Install dependencies
    Write-Host "Installing Node.js dependencies..." -ForegroundColor Cyan
    npm install
    Write-Host "✅ Frontend dependencies installed" -ForegroundColor Green

    Set-Location "../.."
}

# Function to setup database
function Setup-Database {
    Write-Host "`n🗄️  Setting up database..." -ForegroundColor Blue

    if (Test-Command "docker") {
        Write-Host "Starting database services with Docker..." -ForegroundColor Cyan
        docker-compose up -d postgres redis

        # Wait for database to be ready
        Write-Host "Waiting for database to be ready..." -ForegroundColor Cyan
        Start-Sleep -Seconds 10

        # Run database migrations
        Set-Location "backend/admin-service"
        & "venv\Scripts\Activate.ps1"

        Write-Host "Creating database migrations..." -ForegroundColor Cyan
        python -m alembic revision --autogenerate -m "Initial migration"

        Write-Host "Applying database migrations..." -ForegroundColor Cyan
        python -m alembic upgrade head

        Write-Host "Initializing database with default data..." -ForegroundColor Cyan
        python create_migration.py

        Set-Location "../.."
        Write-Host "✅ Database setup completed" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  Docker not available. Please set up PostgreSQL and Redis manually." -ForegroundColor Yellow
        Write-Host "   PostgreSQL: localhost:5432, database: innovative_centre" -ForegroundColor Yellow
        Write-Host "   Redis: localhost:6379" -ForegroundColor Yellow
    }
}

# Main setup function
function Main {
    Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                 INNOVATIVE CENTRE PLATFORM                  ║
║                    Development Setup                        ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Host "`n❌ Prerequisites not met. Please install missing requirements and try again." -ForegroundColor Red
        exit 1
    }

    # Setup environment
    Setup-Environment

    # Setup backend
    Setup-Backend

    # Setup frontend
    Setup-Frontend

    # Setup database
    Setup-Database

    Write-Host @"

🎉 Setup completed successfully!

📚 Next steps:
1. Review and update the .env file with your configuration
2. Start the development servers:
   - Backend: cd backend/admin-service && venv\Scripts\Activate.ps1 && uvicorn main:app --reload
   - Frontend: cd frontend/admin-portal && npm run dev

🌐 Access points:
- Admin Portal: http://localhost:3000
- API Documentation: http://localhost:8000/docs
- API Health Check: http://localhost:8000/health

📖 Default admin credentials:
- Email: <EMAIL>
- Password: changeme (Please change this!)

Happy coding! 🚀
"@ -ForegroundColor Green
}

# Run main function
Main