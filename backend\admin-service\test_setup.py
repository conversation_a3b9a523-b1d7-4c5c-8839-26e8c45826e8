"""
Test script to verify backend setup
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all imports work correctly"""
    try:
        print("Testing imports...")
        
        # Test core imports
        from app.core.config import settings
        print("✓ Core config imported successfully")
        
        # Test database imports
        from app.db.session import get_db, engine
        print("✓ Database session imported successfully")
        
        # Test model imports
        from app.models.user import User
        from app.models.role import Role
        from app.models.audit_log import AuditLog
        print("✓ Models imported successfully")
        
        # Test schema imports
        from app.schemas.user import UserCreate, UserResponse
        from app.schemas.auth import Token
        print("✓ Schemas imported successfully")
        
        # Test service imports
        from app.services.user_service import user_service
        print("✓ Services imported successfully")
        
        # Test API imports
        from app.api.v1.api import api_router
        print("✓ API router imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import error: {e}")
        return False


def test_config():
    """Test configuration settings"""
    try:
        print("\nTesting configuration...")
        
        from app.core.config import settings
        
        print(f"Project Name: {settings.PROJECT_NAME}")
        print(f"API Version: {settings.API_V1_STR}")
        print(f"Database URL: {str(settings.DATABASE_URL)[:50]}...")
        print(f"Debug Mode: {settings.DEBUG}")
        
        print("✅ Configuration loaded successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_database_connection():
    """Test database connection"""
    try:
        print("\nTesting database connection...")
        
        from app.db.session import engine
        from sqlalchemy import text
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            return True
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        print("Note: Make sure PostgreSQL is running and accessible")
        return False


def main():
    """Run all tests"""
    print("🚀 Testing Innovative Centre Admin Service Setup\n")
    
    tests = [
        test_imports,
        test_config,
        test_database_connection,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("🎉 All tests passed! Backend setup is ready.")
        print("\nNext steps:")
        print("1. Run database migrations: alembic upgrade head")
        print("2. Start the server: uvicorn main:app --reload")
        print("3. Visit http://localhost:8000/docs for API documentation")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()
