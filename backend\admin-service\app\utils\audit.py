"""
Audit logging utilities
"""

from typing import Any, Dict, Optional
from datetime import datetime

from fastapi import Request
from sqlalchemy.orm import Session

from app.models.audit_log import AuditLog, AuditActions, AuditResourceTypes
from app.models.user import User


class AuditLogger:
    """Utility class for audit logging"""
    
    @staticmethod
    def log_action(
        db: Session,
        user: Optional[User],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None,
        request: Optional[Request] = None,
        status: str = "SUCCESS",
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> AuditLog:
        """
        Log an audit action
        """
        # Extract request information if available
        endpoint = None
        method = None
        ip_address = None
        user_agent = None
        
        if request:
            endpoint = str(request.url.path)
            method = request.method
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent")
        
        # Create audit log entry
        audit_log = AuditLog(
            user_id=user.id if user else None,
            action=action,
            resource_type=resource_type,
            resource_id=str(resource_id) if resource_id else None,
            endpoint=endpoint,
            method=method,
            ip_address=ip_address,
            user_agent=user_agent,
            old_values=old_values,
            new_values=new_values,
            description=description,
            metadata=metadata,
            status=status,
            error_message=error_message,
        )
        
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)
        
        return audit_log
    
    @staticmethod
    def log_login(
        db: Session,
        user: Optional[User],
        request: Request,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> AuditLog:
        """Log login attempt"""
        action = AuditActions.LOGIN if success else AuditActions.LOGIN_FAILED
        status = "SUCCESS" if success else "FAILED"
        
        return AuditLogger.log_action(
            db=db,
            user=user,
            action=action,
            resource_type=AuditResourceTypes.AUTH,
            description=f"User login {'successful' if success else 'failed'}",
            request=request,
            status=status,
            error_message=error_message,
        )
    
    @staticmethod
    def log_logout(
        db: Session,
        user: User,
        request: Request,
    ) -> AuditLog:
        """Log logout"""
        return AuditLogger.log_action(
            db=db,
            user=user,
            action=AuditActions.LOGOUT,
            resource_type=AuditResourceTypes.AUTH,
            description="User logout",
            request=request,
        )
    
    @staticmethod
    def log_create(
        db: Session,
        user: User,
        resource_type: str,
        resource_id: str,
        new_values: Dict[str, Any],
        request: Optional[Request] = None,
        description: Optional[str] = None,
    ) -> AuditLog:
        """Log resource creation"""
        return AuditLogger.log_action(
            db=db,
            user=user,
            action=AuditActions.CREATE,
            resource_type=resource_type,
            resource_id=resource_id,
            new_values=new_values,
            description=description or f"Created {resource_type}",
            request=request,
        )
    
    @staticmethod
    def log_update(
        db: Session,
        user: User,
        resource_type: str,
        resource_id: str,
        old_values: Dict[str, Any],
        new_values: Dict[str, Any],
        request: Optional[Request] = None,
        description: Optional[str] = None,
    ) -> AuditLog:
        """Log resource update"""
        return AuditLogger.log_action(
            db=db,
            user=user,
            action=AuditActions.UPDATE,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            description=description or f"Updated {resource_type}",
            request=request,
        )
    
    @staticmethod
    def log_delete(
        db: Session,
        user: User,
        resource_type: str,
        resource_id: str,
        old_values: Dict[str, Any],
        request: Optional[Request] = None,
        description: Optional[str] = None,
    ) -> AuditLog:
        """Log resource deletion"""
        return AuditLogger.log_action(
            db=db,
            user=user,
            action=AuditActions.DELETE,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            description=description or f"Deleted {resource_type}",
            request=request,
        )
