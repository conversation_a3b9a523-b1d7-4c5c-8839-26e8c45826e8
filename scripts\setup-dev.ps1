# Development Environment Setup Script for Windows

Write-Host "Setting up Innovative Centre Platform Development Environment..." -ForegroundColor Green

# Check if required tools are installed
function Test-Command($command) {
    try {
        if (Get-Command $command -ErrorAction Stop) {
            return $true
        }
    }
    catch {
        return $false
    }
}

# Check Node.js
if (-not (Test-Command "node")) {
    Write-Host "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check Python
if (-not (Test-Command "python")) {
    Write-Host "Python is not installed. Please install Python 3.11+ from https://python.org/" -ForegroundColor Red
    exit 1
}

# Check Docker (optional)
if (-not (Test-Command "docker")) {
    Write-Host "Docker is not installed. You can install it later for containerized development." -ForegroundColor Yellow
}

Write-Host "Creating environment file..." -ForegroundColor Blue
if (-not (Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host ".env file created from .env.example" -ForegroundColor Green
} else {
    Write-Host ".env file already exists" -ForegroundColor Yellow
}

Write-Host "Setting up backend environment..." -ForegroundColor Blue
Set-Location "backend/admin-service"

# Create virtual environment
if (-not (Test-Path "venv")) {
    python -m venv venv
    Write-Host "Python virtual environment created" -ForegroundColor Green
}

# Activate virtual environment and install dependencies
& "venv\Scripts\Activate.ps1"
if (Test-Path "requirements.txt") {
    pip install -r requirements.txt
    Write-Host "Backend dependencies installed" -ForegroundColor Green
}

# Return to root directory
Set-Location "../.."

Write-Host "Setting up frontend environment..." -ForegroundColor Blue
Set-Location "frontend/admin-portal"

# Install frontend dependencies
if (Test-Path "package.json") {
    npm install
    Write-Host "Frontend dependencies installed" -ForegroundColor Green
}

# Return to root directory
Set-Location "../.."

Write-Host "Development environment setup complete!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Configure your database connection in .env file" -ForegroundColor White
Write-Host "2. Run 'docker-compose up -d postgres redis' to start databases" -ForegroundColor White
Write-Host "3. Run 'scripts/start-dev.ps1' to start development servers" -ForegroundColor White
